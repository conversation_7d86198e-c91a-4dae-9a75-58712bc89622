# SmallDoge WebUI

A model inference WebUI with Gradio frontend and open-webui-compatible backend, specifically designed for SmallDoge models and other transformers-based language models.

## Features

- **FastAPI Backend**: OpenAI-compatible API endpoints
- **Gradio Frontend**: Modern, user-friendly chat interface
- **Model Support**: Optimized for SmallDoge models with `trust_remote_code=True`
- **Open Access**: No authentication required - perfect for open source feature sharing
- **Streaming**: Real-time streaming chat responses
- **Model Management**: Load/unload models dynamically
- **Open-WebUI Compatible**: Backend follows open-webui patterns for compatibility

## Architecture

```
smalldoge-webui/
├── backend/                    # FastAPI backend
│   ├── smalldoge_webui/       # Main package
│   │   ├── main.py            # FastAPI application
│   │   ├── config.py          # Configuration management
│   │   ├── models/            # Pydantic data models
│   │   ├── routers/           # API route handlers
│   │   ├── utils/             # Utility functions
│   │   └── internal/          # Internal services
│   ├── requirements.txt       # Backend dependencies
│   └── start.py              # Backend startup script
├── frontend/                  # Gradio frontend
│   ├── app.py                # Main Gradio application
│   └── requirements.txt      # Frontend dependencies
└── README.md
```

## Quick Start

### Prerequisites

- Python 3.8+
- PyTorch (with CUDA support if using GPU)
- Git

### Installation

1. **Clone the repository**:
   ```bash
   cd webui/smalldoge-webui
   ```

2. **Install backend dependencies**:
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

3. **Install frontend dependencies**:
   ```bash
   cd ../frontend
   pip install -r requirements.txt
   ```

### Running the Application

1. **Start the backend server**:
   ```bash
   cd backend
   python start.py
   ```
   
   The backend will start on `http://localhost:8000`

2. **Start the frontend** (in a new terminal):
   ```bash
   cd frontend
   python app.py
   ```
   
   The frontend will start on `http://localhost:7860`

3. **Access the application**:
   - Open your browser and go to `http://localhost:7860`
   - Start chatting with the models immediately - no login required!

## Configuration

### Environment Variables

Create a `.env` file in the backend directory:

```env
# Database
DATABASE_URL=sqlite:///./data/smalldoge_webui.db

# Security
WEBUI_SECRET_KEY=your-secret-key-here
JWT_EXPIRES_IN=7d

# Models
DEFAULT_MODELS=SmallDoge/Doge-160M
MODEL_CACHE_DIR=./data/models

# Server
HOST=0.0.0.0
PORT=8000
ENV=dev

# CORS
ENABLE_CORS=true
CORS_ALLOW_ORIGIN=*

# Authentication (disabled for open access)
WEBUI_AUTH=false
ENABLE_SIGNUP=false

# Model Inference
MAX_TOKENS=2048
TEMPERATURE=0.7
TOP_P=0.9
TOP_K=50
DEVICE=auto
TORCH_DTYPE=auto
```

### Model Configuration

The application supports various SmallDoge models:
- `SmallDoge/Doge-160M` (default)
- `SmallDoge/Doge-1B`
- `SmallDoge/Doge-7B`

Models are automatically loaded with `trust_remote_code=True` for compatibility.

## API Documentation

### Authentication (Optional - Disabled by Default)

- `POST /api/v1/auths/signin` - User signin (disabled)
- `POST /api/v1/auths/signup` - User signup (disabled)
- `GET /api/v1/auths/verify` - Verify token (disabled)

### OpenAI-Compatible Endpoints

- `GET /openai/models` - List available models
- `POST /openai/chat/completions` - Chat completions (streaming and non-streaming)

### Model Management

- `GET /api/v1/models` - List available models
- `GET /api/v1/models/loaded` - List loaded models
- `POST /api/v1/models/{model_id}/load` - Load model (admin only)
- `POST /api/v1/models/{model_id}/unload` - Unload model (admin only)

### User Management

- `GET /api/v1/users/me` - Get current user info
- `PUT /api/v1/users/me` - Update user profile
- `PUT /api/v1/users/password` - Update password

## Development

### Backend Development

1. **Install development dependencies**:
   ```bash
   pip install pytest black isort flake8
   ```

2. **Run tests**:
   ```bash
   pytest
   ```

3. **Format code**:
   ```bash
   black .
   isort .
   ```

4. **Start with auto-reload**:
   ```bash
   python start.py --reload
   ```

### Frontend Development

The Gradio frontend automatically reloads when files change during development.

### Adding New Models

1. Add model ID to `MODEL_CONFIG.SMALLDOGE_MODELS` in `constants.py`
2. Update model-specific configurations if needed
3. Test model loading and inference

## Deployment

### Production Deployment

1. **Set environment variables**:
   ```bash
   export ENV=prod
   export WEBUI_SECRET_KEY=your-secure-secret-key
   export DATABASE_URL=postgresql://user:pass@localhost/dbname
   ```

2. **Use a production WSGI server**:
   ```bash
   pip install gunicorn
   gunicorn smalldoge_webui.main:app -w 4 -k uvicorn.workers.UvicornWorker
   ```

3. **Set up reverse proxy** (nginx, Apache, etc.)

### Docker Deployment

Create a `Dockerfile`:

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install backend
COPY backend/requirements.txt ./backend/
RUN pip install -r backend/requirements.txt

# Install frontend
COPY frontend/requirements.txt ./frontend/
RUN pip install -r frontend/requirements.txt

# Copy application
COPY . .

# Expose ports
EXPOSE 8000 7860

# Start script
CMD ["python", "backend/start.py"]
```

## Troubleshooting

### Common Issues

1. **Model loading fails**:
   - Ensure `trust_remote_code=True` is set
   - Check available GPU memory
   - Verify model ID is correct

2. **Authentication errors**:
   - Check `WEBUI_SECRET_KEY` is set
   - Verify JWT token is not expired

3. **CORS issues**:
   - Set `ENABLE_CORS=true`
   - Configure `CORS_ALLOW_ORIGIN` properly

### Logs

Backend logs are available in the console output. For production, configure proper logging:

```python
import logging
logging.basicConfig(level=logging.INFO)
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Based on [open-webui](https://github.com/open-webui/open-webui) architecture patterns
- Built with [FastAPI](https://fastapi.tiangolo.com/) and [Gradio](https://gradio.app/)
- Powered by [Transformers](https://huggingface.co/transformers/) and [PyTorch](https://pytorch.org/)
