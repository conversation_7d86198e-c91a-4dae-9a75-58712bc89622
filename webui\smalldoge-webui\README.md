# 🐕 SmallDoge WebUI

**Open Source Feature Sharing Platform** - A model inference WebUI designed for collaborative AI experimentation and feature sharing without barriers.

## 🎯 Project Goal

SmallDoge WebUI is built for **open source feature sharing** - enabling researchers, developers, and AI enthusiasts to:
- Share AI model capabilities instantly without setup friction
- Collaborate on model experiments in real-time
- Demonstrate model features to the community
- Provide immediate access to AI capabilities for everyone

**No login, no barriers, just pure AI interaction!**

## ✨ Features

- **🚀 Instant Access**: Zero authentication - start chatting immediately
- **🔧 FastAPI Backend**: OpenAI-compatible API endpoints
- **🎨 Gradio Frontend**: Modern, intuitive chat interface
- **🤖 SmallDoge Optimized**: Built for SmallDoge models with `trust_remote_code=True`
- **⚡ Real-time Streaming**: Live chat responses as they generate
- **🔄 Dynamic Models**: Load/unload models on demand
- **🔗 API Compatible**: Works with existing OpenAI-compatible tools
- **🌐 Open Architecture**: Based on proven open-webui patterns

## 🏗️ Architecture

**Open Source Sharing First** - Designed for immediate access and collaboration:

```
smalldoge-webui/
├── backend/                    # FastAPI backend (no auth required)
│   ├── smalldoge_webui/       # Main package
│   │   ├── main.py            # FastAPI application
│   │   ├── config.py          # Configuration management
│   │   ├── models/            # Data models (chats, models)
│   │   ├── routers/           # API routes (open access)
│   │   ├── utils/             # Utility functions
│   │   └── internal/          # Internal services
│   ├── requirements.txt       # Backend dependencies
│   └── start.py              # Backend startup script
├── frontend/                  # Gradio frontend (instant access)
│   ├── app.py                # Main Gradio application
│   └── requirements.txt      # Frontend dependencies
└── README.md
```

**Key Design Principles:**
- 🔓 **Open by Default**: No authentication barriers
- 🤝 **Collaboration Ready**: Built for sharing and experimentation
- ⚡ **Fast Setup**: Get running in minutes, not hours
- 🔧 **Developer Friendly**: Clean APIs and extensible architecture

## Quick Start

### Prerequisites

- Python 3.8+
- PyTorch (with CUDA support if using GPU)
- Git

### Installation

1. **Clone the repository**:
   ```bash
   cd webui/smalldoge-webui
   ```

2. **Install backend dependencies**:
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

3. **Install frontend dependencies**:
   ```bash
   cd ../frontend
   pip install -r requirements.txt
   ```

### Running the Application

1. **Start the backend server**:
   ```bash
   cd backend
   python start.py
   ```
   
   The backend will start on `http://localhost:8000`

2. **Start the frontend** (in a new terminal):
   ```bash
   cd frontend
   python app.py
   ```
   
   The frontend will start on `http://localhost:7860`

3. **Start sharing AI features**:
   - Open your browser and go to `http://localhost:7860`
   - **No setup, no login, no barriers** - start chatting immediately!
   - Share the URL with others for instant collaboration

## ⚙️ Configuration

### Environment Variables (Optional)

The WebUI works out-of-the-box with sensible defaults. For customization, create a `.env` file in the backend directory:

```env
# Database (SQLite by default)
DATABASE_URL=sqlite:///./data/smalldoge_webui.db

# Models
DEFAULT_MODELS=SmallDoge/Doge-160M
MODEL_CACHE_DIR=./data/models

# Server
HOST=0.0.0.0
PORT=8000
ENV=dev

# CORS (enabled for open sharing)
ENABLE_CORS=true
CORS_ALLOW_ORIGIN=*

# Model Inference
MAX_TOKENS=2048
TEMPERATURE=0.7
TOP_P=0.9
TOP_K=50
DEVICE=auto
TORCH_DTYPE=auto
```

**Note**: Authentication is permanently disabled to support the open source sharing mission.

### Model Configuration

The application supports various SmallDoge models:
- `SmallDoge/Doge-160M` (default)
- `SmallDoge/Doge-1B`
- `SmallDoge/Doge-7B`

Models are automatically loaded with `trust_remote_code=True` for compatibility.

## 📡 API Documentation

**Open Access APIs** - No authentication required for any endpoint!

### 🤖 OpenAI-Compatible Endpoints

Perfect for integrating with existing tools and libraries:

- `GET /openai/models` - List available models
- `POST /openai/chat/completions` - Chat completions (streaming and non-streaming)
- `GET /openai/models/{model_id}/capabilities` - Get model capabilities

### 🔧 Model Management

Anyone can manage models - perfect for collaborative experimentation:

- `GET /api/v1/models` - List available models
- `GET /api/v1/models/loaded` - List currently loaded models
- `GET /api/v1/models/{model_id}/status` - Get model status
- `POST /api/v1/models/{model_id}/load` - Load a model
- `POST /api/v1/models/{model_id}/unload` - Unload a model
- `GET /api/v1/models/{model_id}/health` - Health check a model

### 💬 Chat Management

- `GET /api/v1/chats` - List chats
- `POST /api/v1/chats` - Create new chat
- `GET /api/v1/chats/{chat_id}` - Get specific chat
- `PUT /api/v1/chats/{chat_id}` - Update chat
- `DELETE /api/v1/chats/{chat_id}` - Delete chat

## Development

### Backend Development

1. **Install development dependencies**:
   ```bash
   pip install pytest black isort flake8
   ```

2. **Run tests**:
   ```bash
   pytest
   ```

3. **Format code**:
   ```bash
   black .
   isort .
   ```

4. **Start with auto-reload**:
   ```bash
   python start.py --reload
   ```

### Frontend Development

The Gradio frontend automatically reloads when files change during development.

### Adding New Models

1. Add model ID to `MODEL_CONFIG.SMALLDOGE_MODELS` in `constants.py`
2. Update model-specific configurations if needed
3. Test model loading and inference

## Deployment

### Production Deployment

1. **Set environment variables**:
   ```bash
   export ENV=prod
   export WEBUI_SECRET_KEY=your-secure-secret-key
   export DATABASE_URL=postgresql://user:pass@localhost/dbname
   ```

2. **Use a production WSGI server**:
   ```bash
   pip install gunicorn
   gunicorn smalldoge_webui.main:app -w 4 -k uvicorn.workers.UvicornWorker
   ```

3. **Set up reverse proxy** (nginx, Apache, etc.)

### Docker Deployment

Create a `Dockerfile`:

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install backend
COPY backend/requirements.txt ./backend/
RUN pip install -r backend/requirements.txt

# Install frontend
COPY frontend/requirements.txt ./frontend/
RUN pip install -r frontend/requirements.txt

# Copy application
COPY . .

# Expose ports
EXPOSE 8000 7860

# Start script
CMD ["python", "backend/start.py"]
```

## Troubleshooting

### Common Issues

1. **Model loading fails**:
   - Ensure `trust_remote_code=True` is set
   - Check available GPU memory
   - Verify model ID is correct

2. **Authentication errors**:
   - Check `WEBUI_SECRET_KEY` is set
   - Verify JWT token is not expired

3. **CORS issues**:
   - Set `ENABLE_CORS=true`
   - Configure `CORS_ALLOW_ORIGIN` properly

### Logs

Backend logs are available in the console output. For production, configure proper logging:

```python
import logging
logging.basicConfig(level=logging.INFO)
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing to Open Source AI

SmallDoge WebUI embodies the spirit of open collaboration. We encourage:

- **Feature Sharing**: Deploy your instance and share cool AI capabilities
- **Model Experiments**: Try new models and share your findings
- **Community Building**: Help others discover and use AI tools
- **Open Development**: Contribute code, ideas, and feedback

### Ways to Contribute

1. **Share Your Instance**: Deploy and share your WebUI with the community
2. **Add Models**: Integrate new models and share configurations
3. **Improve Features**: Submit PRs for new functionality
4. **Report Issues**: Help us improve the platform
5. **Spread the Word**: Share the project with other AI enthusiasts

## 🙏 Acknowledgments

- Inspired by [open-webui](https://github.com/open-webui/open-webui) architecture patterns
- Built with [FastAPI](https://fastapi.tiangolo.com/) and [Gradio](https://gradio.app/)
- Powered by [Transformers](https://huggingface.co/transformers/) and [PyTorch](https://pytorch.org/)
- Made possible by the open source AI community

---

**🎯 Mission**: Making AI accessible to everyone through open source feature sharing.

**🚀 Vision**: A world where AI capabilities are shared freely and collaboratively.
