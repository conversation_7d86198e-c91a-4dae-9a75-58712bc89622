"""
User models for SmallDoge WebUI
Based on open-webui's user model patterns
"""

import time
from typing import Optional, Dict, Any
from pydantic import BaseModel, EmailStr, ConfigDict
from sqlalchemy import Column, String, Integer, JSO<PERSON>, Boolean

from smalldoge_webui.internal.db import Base
from smalldoge_webui.constants import ROLES


####################
# Database Models
####################

class User(Base):
    __tablename__ = "users"

    id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    email = Column(String, unique=True, nullable=False)
    role = Column(String, default=ROLES.PENDING)
    profile_image_url = Column(String, default="/user.png")
    
    # Password hash (using bcrypt)
    password_hash = Column(String, nullable=False)
    
    # API key for authentication
    api_key = Column(String, unique=True, nullable=True)
    
    # User settings and preferences
    settings = Column(JSON, nullable=True)
    info = Column(JSON, nullable=True)
    
    # OAuth integration
    oauth_sub = Column(String, nullable=True)
    
    # Status and timestamps
    is_active = Column(Boolean, default=True)
    last_active_at = Column(Integer, default=lambda: int(time.time()))
    created_at = Column(Integer, default=lambda: int(time.time()))
    updated_at = Column(Integer, default=lambda: int(time.time()))


####################
# Pydantic Models
####################

class UserSettings(BaseModel):
    """User settings and preferences"""
    theme: Optional[str] = "light"
    language: Optional[str] = "en"
    timezone: Optional[str] = "UTC"
    notifications: Optional[bool] = True
    default_model: Optional[str] = None
    chat_settings: Optional[Dict[str, Any]] = None


class UserModel(BaseModel):
    """User data model for API responses"""
    model_config = ConfigDict(from_attributes=True)
    
    id: str
    name: str
    email: str
    role: str
    profile_image_url: str
    
    api_key: Optional[str] = None
    settings: Optional[UserSettings] = None
    info: Optional[Dict[str, Any]] = None
    oauth_sub: Optional[str] = None
    
    is_active: bool
    last_active_at: int
    created_at: int
    updated_at: int


####################
# Response Models
####################

class UserResponse(BaseModel):
    """Public user information for responses"""
    id: str
    name: str
    email: str
    role: str
    profile_image_url: str


class UserNameResponse(BaseModel):
    """Minimal user information"""
    id: str
    name: str
    role: str
    profile_image_url: str


class UserListResponse(BaseModel):
    """Response for user list endpoints"""
    users: list[UserModel]
    total: int


####################
# Form Models
####################

class UserForm(BaseModel):
    """Form for creating/updating users"""
    name: str
    email: EmailStr
    role: str = ROLES.USER
    profile_image_url: Optional[str] = "/user.png"
    settings: Optional[UserSettings] = None
    info: Optional[Dict[str, Any]] = None


class UserUpdateForm(BaseModel):
    """Form for updating user information"""
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    profile_image_url: Optional[str] = None
    settings: Optional[UserSettings] = None
    info: Optional[Dict[str, Any]] = None


class UserRoleUpdateForm(BaseModel):
    """Form for updating user role (admin only)"""
    id: str
    role: str


class UpdatePasswordForm(BaseModel):
    """Form for updating user password"""
    current_password: str
    new_password: str


class ProfileImageUrlForm(BaseModel):
    """Form for updating profile image URL"""
    profile_image_url: str


####################
# Utility Functions
####################

def create_user_id(email: str) -> str:
    """Create a unique user ID from email"""
    import hashlib
    return hashlib.md5(email.encode()).hexdigest()


def validate_role(role: str) -> bool:
    """Validate user role"""
    return role in [ROLES.ADMIN, ROLES.USER, ROLES.PENDING]


def get_default_user_settings() -> UserSettings:
    """Get default user settings"""
    return UserSettings(
        theme="light",
        language="en",
        timezone="UTC",
        notifications=True,
        default_model="SmallDoge/Doge-160M",
        chat_settings={
            "max_tokens": 2048,
            "temperature": 0.7,
            "top_p": 0.9,
            "stream": True
        }
    )
