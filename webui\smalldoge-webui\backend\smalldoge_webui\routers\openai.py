"""
OpenAI-compatible API router for SmallDoge WebUI
Provides OpenAI-compatible endpoints for chat completions and models
"""

import json
import logging
import time
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.responses import StreamingResponse

from smalldoge_webui.models.users import UserModel
from smalldoge_webui.models.chats import ChatCompletionRequest
from smalldoge_webui.models.models import OpenAIModelResponse, OpenAIModelsResponse
from smalldoge_webui.utils.auth import get_verified_user
from smalldoge_webui.utils.chat import generate_chat_completion, format_chat_messages, validate_chat_messages
from smalldoge_webui.utils.models import get_available_models, get_model_capabilities, get_model_context_length
from smalldoge_webui.constants import ERROR_MESSAGES

log = logging.getLogger(__name__)
router = APIRouter()


####################
# Models Endpoint
####################

@router.get("/models", response_model=OpenAIModelsResponse)
async def list_models(user: UserModel = Depends(get_verified_user)):
    """
    List available models (OpenAI-compatible)
    
    Args:
        user: Authenticated user
    
    Returns:
        OpenAIModelsResponse: List of available models
    """
    try:
        available_models = get_available_models()
        
        model_list = []
        for model_id in available_models:
            model_response = OpenAIModelResponse(
                id=model_id,
                object="model",
                created=int(time.time()),
                owned_by="smalldoge-webui"
            )
            model_list.append(model_response)
        
        return OpenAIModelsResponse(
            object="list",
            data=model_list
        )
    
    except Exception as e:
        log.error(f"Error listing models: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )


@router.get("/models/{model_id}", response_model=OpenAIModelResponse)
async def get_model(model_id: str, user: UserModel = Depends(get_verified_user)):
    """
    Get specific model information (OpenAI-compatible)
    
    Args:
        model_id: Model identifier
        user: Authenticated user
    
    Returns:
        OpenAIModelResponse: Model information
    """
    try:
        available_models = get_available_models()
        
        if model_id not in available_models:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.MODEL_NOT_FOUND(model_id)
            )
        
        return OpenAIModelResponse(
            id=model_id,
            object="model",
            created=int(time.time()),
            owned_by="smalldoge-webui"
        )
    
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error getting model {model_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )


####################
# Chat Completions Endpoint
####################

@router.post("/chat/completions")
async def create_chat_completion(
    request: Request,
    form_data: Dict[str, Any],
    user: UserModel = Depends(get_verified_user)
):
    """
    Create chat completion (OpenAI-compatible)
    
    Args:
        request: FastAPI request object
        form_data: Chat completion request data
        user: Authenticated user
    
    Returns:
        StreamingResponse or Dict: Chat completion response
    """
    try:
        # Validate request data
        if "model" not in form_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Model is required"
            )
        
        if "messages" not in form_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Messages are required"
            )
        
        # Validate messages format
        if not validate_chat_messages(form_data["messages"]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=ERROR_MESSAGES.INVALID_CHAT_FORMAT
            )
        
        # Check if model is available
        available_models = get_available_models()
        if form_data["model"] not in available_models:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.MODEL_NOT_FOUND(form_data["model"])
            )
        
        # Format messages
        formatted_messages = format_chat_messages(form_data["messages"])
        form_data["messages"] = [msg.model_dump() for msg in formatted_messages]
        
        # Set default values
        form_data.setdefault("temperature", 0.7)
        form_data.setdefault("top_p", 0.9)
        form_data.setdefault("max_tokens", 2048)
        form_data.setdefault("stream", False)
        
        # Generate chat completion
        return await generate_chat_completion(
            request=request,
            form_data=form_data,
            user=user,
            bypass_filter=False
        )
    
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Chat completion error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.MODEL_INFERENCE_ERROR(str(e))
        )


####################
# Completions Endpoint (Legacy)
####################

@router.post("/completions")
async def create_completion(
    request: Request,
    form_data: Dict[str, Any],
    user: UserModel = Depends(get_verified_user)
):
    """
    Create text completion (OpenAI-compatible, legacy)
    
    Args:
        request: FastAPI request object
        form_data: Completion request data
        user: Authenticated user
    
    Returns:
        Dict: Completion response
    """
    try:
        # Convert completion request to chat completion format
        if "prompt" not in form_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Prompt is required"
            )
        
        if "model" not in form_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Model is required"
            )
        
        # Convert prompt to chat messages
        chat_messages = [
            {"role": "user", "content": form_data["prompt"]}
        ]
        
        # Create chat completion request
        chat_form_data = {
            "model": form_data["model"],
            "messages": chat_messages,
            "temperature": form_data.get("temperature", 0.7),
            "top_p": form_data.get("top_p", 0.9),
            "max_tokens": form_data.get("max_tokens", 2048),
            "stream": form_data.get("stream", False),
            "stop": form_data.get("stop"),
            "presence_penalty": form_data.get("presence_penalty", 0.0),
            "frequency_penalty": form_data.get("frequency_penalty", 0.0),
            "user": form_data.get("user")
        }
        
        # Generate completion using chat completion endpoint
        response = await generate_chat_completion(
            request=request,
            form_data=chat_form_data,
            user=user,
            bypass_filter=False
        )
        
        # Convert chat completion response to completion format
        if isinstance(response, StreamingResponse):
            return response
        else:
            # Convert response format
            if "choices" in response and response["choices"]:
                choice = response["choices"][0]
                if "message" in choice:
                    choice["text"] = choice["message"]["content"]
                    del choice["message"]
            
            response["object"] = "text_completion"
            return response
    
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Completion error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.MODEL_INFERENCE_ERROR(str(e))
        )


####################
# Model Information Endpoints
####################

@router.get("/models/{model_id}/capabilities")
async def get_model_capabilities_endpoint(
    model_id: str,
    user: UserModel = Depends(get_verified_user)
):
    """
    Get model capabilities
    
    Args:
        model_id: Model identifier
        user: Authenticated user
    
    Returns:
        Dict: Model capabilities
    """
    try:
        available_models = get_available_models()
        
        if model_id not in available_models:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.MODEL_NOT_FOUND(model_id)
            )
        
        capabilities = get_model_capabilities(model_id)
        context_length = get_model_context_length(model_id)
        
        return {
            "model_id": model_id,
            "capabilities": capabilities,
            "context_length": context_length,
            "supports_streaming": True,
            "supports_functions": False,
            "supports_vision": False
        }
    
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error getting model capabilities for {model_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )
