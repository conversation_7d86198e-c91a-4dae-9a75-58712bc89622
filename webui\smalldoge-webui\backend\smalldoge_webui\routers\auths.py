"""
Authentication router for SmallDoge WebUI
Based on open-webui's authentication patterns
"""

import logging
import time
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from smalldoge_webui.internal.db import get_db
from smalldoge_webui.models.users import User, UserModel, create_user_id, get_default_user_settings
from smalldoge_webui.models.auths import (
    SigninForm,
    SignupForm,
    SigninResponse,
    Token,
    UserResponse,
    ApiKeyForm,
    ApiKeyResponse
)
from smalldoge_webui.utils.auth import (
    authenticate_user,
    create_access_token,
    get_password_hash,
    validate_password,
    generate_api_key,
    get_verified_user,
    get_user_by_email
)
from smalldoge_webui.constants import ERROR_MESSAGES, SUCCESS_MESSAGES, ROLES
from smalldoge_webui.env import ENABLE_SIGNUP, DEFAULT_USER_ROLE

log = logging.getLogger(__name__)
router = APIRouter()


####################
# Authentication Endpoints
####################

@router.post("/signin", response_model=SigninResponse)
async def signin(form_data: SigninForm, db: Session = Depends(get_db)):
    """
    User signin endpoint
    
    Args:
        form_data: Signin form with email and password
        db: Database session
    
    Returns:
        SigninResponse: JWT token and user information
    """
    try:
        # Authenticate user
        user = authenticate_user(form_data.email, form_data.password, db)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=ERROR_MESSAGES.INVALID_CRED
            )
        
        # Create access token
        access_token = create_access_token(
            data={"user_id": user.id, "email": user.email, "role": user.role}
        )
        
        # Update last active time
        db_user = db.query(User).filter(User.id == user.id).first()
        if db_user:
            db_user.last_active_at = int(time.time())
            db.commit()
        
        log.info(f"User {user.email} signed in successfully")
        
        return SigninResponse(
            token=access_token,
            token_type="bearer",
            id=user.id,
            email=user.email,
            name=user.name,
            role=user.role,
            profile_image_url=user.profile_image_url
        )
    
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Signin error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )


@router.post("/signup", response_model=UserResponse)
async def signup(form_data: SignupForm, db: Session = Depends(get_db)):
    """
    User signup endpoint
    
    Args:
        form_data: Signup form with user details
        db: Database session
    
    Returns:
        UserResponse: Created user information
    """
    try:
        # Check if signup is enabled
        if not ENABLE_SIGNUP:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Signup is disabled"
            )
        
        # Check if user already exists
        existing_user = get_user_by_email(form_data.email, db)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=ERROR_MESSAGES.EMAIL_TAKEN
            )
        
        # Validate password
        if not validate_password(form_data.password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Password does not meet requirements"
            )
        
        # Create user
        user_id = create_user_id(form_data.email)
        password_hash = get_password_hash(form_data.password)
        
        # Check if this is the first user (make them admin)
        user_count = db.query(User).count()
        user_role = ROLES.ADMIN if user_count == 0 else DEFAULT_USER_ROLE
        
        db_user = User(
            id=user_id,
            name=form_data.name,
            email=form_data.email,
            password_hash=password_hash,
            role=user_role,
            profile_image_url=form_data.profile_image_url or "/user.png",
            settings=get_default_user_settings().model_dump(),
            is_active=True,
            created_at=int(time.time()),
            updated_at=int(time.time()),
            last_active_at=int(time.time())
        )
        
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        
        log.info(f"User {form_data.email} signed up successfully with role {user_role}")
        
        return UserResponse(
            id=db_user.id,
            name=db_user.name,
            email=db_user.email,
            role=db_user.role,
            profile_image_url=db_user.profile_image_url
        )
    
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Signup error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )


@router.post("/signout")
async def signout(user: UserModel = Depends(get_verified_user)):
    """
    User signout endpoint
    
    Args:
        user: Authenticated user
    
    Returns:
        dict: Success message
    """
    # Note: JWT tokens are stateless, so we can't invalidate them server-side
    # In a production environment, you might want to implement a token blacklist
    log.info(f"User {user.email} signed out")
    return {"message": "Signed out successfully"}


####################
# API Key Management
####################

@router.post("/api-key", response_model=ApiKeyResponse)
async def create_api_key(
    form_data: ApiKeyForm,
    user: UserModel = Depends(get_verified_user),
    db: Session = Depends(get_db)
):
    """
    Create API key for user
    
    Args:
        form_data: API key creation form
        user: Authenticated user
        db: Database session
    
    Returns:
        ApiKeyResponse: Generated API key
    """
    try:
        # Generate new API key
        api_key = generate_api_key()
        
        # Update user with new API key
        db_user = db.query(User).filter(User.id == user.id).first()
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.USER_NOT_FOUND
            )
        
        db_user.api_key = api_key
        db_user.updated_at = int(time.time())
        db.commit()
        
        log.info(f"API key created for user {user.email}")
        
        return ApiKeyResponse(
            api_key=api_key,
            name=form_data.name,
            created_at=int(time.time()),
            expires_at=form_data.expires_at
        )
    
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"API key creation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )


@router.delete("/api-key")
async def delete_api_key(
    user: UserModel = Depends(get_verified_user),
    db: Session = Depends(get_db)
):
    """
    Delete user's API key
    
    Args:
        user: Authenticated user
        db: Database session
    
    Returns:
        dict: Success message
    """
    try:
        # Remove API key from user
        db_user = db.query(User).filter(User.id == user.id).first()
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.USER_NOT_FOUND
            )
        
        db_user.api_key = None
        db_user.updated_at = int(time.time())
        db.commit()
        
        log.info(f"API key deleted for user {user.email}")
        
        return {"message": "API key deleted successfully"}
    
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"API key deletion error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )


####################
# Token Validation
####################

@router.get("/verify")
async def verify_token(user: UserModel = Depends(get_verified_user)):
    """
    Verify JWT token and return user information
    
    Args:
        user: Authenticated user
    
    Returns:
        UserResponse: User information
    """
    return UserResponse(
        id=user.id,
        name=user.name,
        email=user.email,
        role=user.role,
        profile_image_url=user.profile_image_url
    )
