# SmallDoge WebUI 快速启动指南

## 概述

SmallDoge WebUI 是一个基于 open-webui 架构的 AI 模型推理平台，使用 FastAPI 后端和 Gradio 前端，支持 transformers 模型推理。

## 最新改进 ✨

### 前端改进
- ✅ 增强的错误处理和用户反馈
- ✅ 模型加载状态显示
- ✅ 实时模型状态监控
- ✅ 改进的对话体验（加载指示器）
- ✅ 详细的后端连接状态

### 后端改进
- ✅ 模型加载/卸载 API 端点
- ✅ 增强的健康检查信息
- ✅ 模型状态监控
- ✅ 更好的错误处理

## 快速启动

### 1. 启动后端服务

```bash
cd webui/smalldoge-webui/backend

# Windows
start_windows.bat

# 或手动启动
python -m smalldoge_webui.main
```

后端将在 http://localhost:8000 启动

### 2. 启动前端服务

```bash
cd webui/smalldoge-webui/frontend

# Windows
start_frontend.bat

# 或手动启动
python app.py
```

前端将在 http://localhost:7860 启动

### 3. 访问应用

打开浏览器访问 http://localhost:7860

## 使用指南

### 首次使用

1. **注册账户**
   - 在前端界面点击"注册"标签
   - 填写姓名、邮箱和密码
   - 点击"注册"按钮

2. **登录系统**
   - 使用注册的邮箱和密码登录
   - 登录成功后进入主界面

### 模型管理

#### 创建模型
1. 进入"模型管理"标签
2. 填写模型信息：
   - **模型ID**: 唯一标识符（如：gpt2-small）
   - **模型名称**: 显示名称（如：GPT-2 Small）
   - **模型路径**: HuggingFace 模型路径（如：gpt2）
   - **模型类型**: 选择 transformers
3. 点击"创建模型"

### 对话功能

1. **选择模型**
   - 在对话界面选择已创建的模型
   - 点击"加载模型"按钮（新功能）
   - 等待模型加载完成

2. **开始对话**
   - 在消息输入框输入问题
   - 点击"发送"或按 Enter
   - 支持流式响应，实时显示回复

3. **模型状态监控**
   - 右侧显示模型信息和状态
   - 包括加载状态、设备信息、内存使用等
