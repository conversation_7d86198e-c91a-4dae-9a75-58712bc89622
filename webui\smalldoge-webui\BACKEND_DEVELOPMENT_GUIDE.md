# SmallDoge WebUI Backend Development Guide

## Overview

This document provides a backend development guide for the SmallDoge WebUI project based on open-webui's backend architecture. The backend uses FastAPI framework, while the frontend will use Gradio instead of open-webui's default frontend. The inference framework uses transformers.

## Architecture Design

### Core Components

1. **FastAPI Application Main** (`main.py`)
2. **Router Modules** (`routers/`)
3. **Data Models** (`models/`)
4. **Configuration Management** (`config.py`)
5. **Authentication System** (`utils/auth.py`)
6. **Utility Functions** (`utils/`)
7. **Model Inference** (`utils/models.py`)
8. **Chat Handling** (`utils/chat.py`)

### Directory Structure

```
smalldoge-webui/
├── backend/                    # Backend code, including FastAPI server, Transformers inference
│   ├── smalldoge_webui/       # Main package
│   │   ├── __init__.py
│   │   ├── main.py            # FastAPI application entry point
│   │   ├── config.py          # Configuration management
│   │   ├── env.py             # Environment variables
│   │   ├── constants.py       # Application constants
│   │   ├── models/            # Pydantic data models
│   │   │   ├── __init__.py
│   │   │   ├── users.py       # User models
│   │   │   ├── chats.py       # Chat models
│   │   │   ├── models.py      # Model management models
│   │   │   └── auths.py       # Authentication models
│   │   ├── routers/           # API route handlers
│   │   │   ├── __init__.py
│   │   │   ├── auths.py       # Authentication endpoints
│   │   │   ├── users.py       # User management
│   │   │   ├── chats.py       # Chat management
│   │   │   ├── models.py      # Model management
│   │   │   └── openai.py      # OpenAI-compatible endpoints
│   │   ├── utils/             # Utility functions
│   │   │   ├── __init__.py
│   │   │   ├── auth.py        # Authentication utilities
│   │   │   ├── chat.py        # Chat handling
│   │   │   ├── models.py      # Model management utilities
│   │   │   └── transformers_inference.py  # Transformers inference
│   │   └── internal/          # Internal services
│   │       ├── __init__.py
│   │       └── db.py          # Database management
│   ├── requirements.txt       # Python dependencies
│   └── start.py              # Application startup script
├── frontend/                  # Gradio frontend
│   ├── app.py                # Main Gradio application
│   ├── components/           # UI components
│   └── utils/                # Frontend utilities
└── README.md

```

## Implementation Plan

### Phase 1: Backend Infrastructure
1. Set up FastAPI application structure
2. Implement configuration management
3. Create database models and schemas
4. Set up authentication system

### Phase 2: Model Inference Integration
1. Implement transformers-based model loading
2. Create chat completion endpoints
3. Add streaming response support
4. Integrate SmallDoge model support

### Phase 3: API Endpoints
1. Authentication endpoints (`/api/v1/auths`)
2. User management (`/api/v1/users`)
3. Chat management (`/api/v1/chats`)
4. Model management (`/api/v1/models`)
5. OpenAI-compatible endpoints (`/openai/chat/completions`)

### Phase 4: Gradio Frontend
1. Create chat interface
2. Implement model selection
3. Add settings panel
4. Integrate with backend API

### Phase 5: Testing and Deployment
1. Unit tests for backend components
2. Integration tests for API endpoints
3. End-to-end testing
4. Deployment configuration
