"""
Authentication utilities for SmallDoge WebUI
Based on open-webui's authentication patterns
"""

import logging
import secrets
from datetime import datetime, timedelta
from typing import Optional, Union

import bcrypt
import jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from smalldoge_webui.env import WEBUI_SECRET_KEY, JWT_EXPIRES_IN
from smalldoge_webui.internal.db import get_db
from smalldoge_webui.models.users import User, UserModel
from smalldoge_webui.constants import ERROR_MESSAGES, ROLES, SECURITY

log = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer(auto_error=False)


####################
# Password Utilities
####################

def get_password_hash(password: str) -> str:
    """Hash a password using bcrypt"""
    salt = bcrypt.gensalt()
    return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    try:
        return bcrypt.checkpw(
            plain_password.encode('utf-8'), 
            hashed_password.encode('utf-8')
        )
    except Exception as e:
        log.error(f"Password verification error: {e}")
        return False


def validate_password(password: str) -> bool:
    """Validate password strength"""
    if len(password) < SECURITY.MIN_PASSWORD_LENGTH:
        return False
    
    if SECURITY.REQUIRE_UPPERCASE and not any(c.isupper() for c in password):
        return False
    
    if SECURITY.REQUIRE_LOWERCASE and not any(c.islower() for c in password):
        return False
    
    if SECURITY.REQUIRE_NUMBERS and not any(c.isdigit() for c in password):
        return False
    
    if SECURITY.REQUIRE_SPECIAL_CHARS and not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
        return False
    
    return True


####################
# JWT Token Utilities
####################

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token"""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        # Parse JWT_EXPIRES_IN (e.g., "7d", "24h", "30m")
        expire = parse_jwt_expires_in(JWT_EXPIRES_IN)
    
    to_encode.update({"exp": expire})
    
    try:
        encoded_jwt = jwt.encode(
            to_encode, 
            WEBUI_SECRET_KEY, 
            algorithm=SECURITY.ALGORITHM
        )
        return encoded_jwt
    except Exception as e:
        log.error(f"Token creation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create access token"
        )


def decode_token(token: str) -> dict:
    """Decode JWT token"""
    try:
        payload = jwt.decode(
            token, 
            WEBUI_SECRET_KEY, 
            algorithms=[SECURITY.ALGORITHM]
        )
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired"
        )
    except jwt.JWTError as e:
        log.error(f"Token decode error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.INVALID_TOKEN
        )


def parse_jwt_expires_in(expires_in: str) -> datetime:
    """Parse JWT expires in string to datetime"""
    try:
        if expires_in == "-1":
            # Never expire (set to 100 years from now)
            return datetime.utcnow() + timedelta(days=36500)
        
        unit = expires_in[-1].lower()
        value = int(expires_in[:-1])
        
        if unit == 'd':
            return datetime.utcnow() + timedelta(days=value)
        elif unit == 'h':
            return datetime.utcnow() + timedelta(hours=value)
        elif unit == 'm':
            return datetime.utcnow() + timedelta(minutes=value)
        elif unit == 's':
            return datetime.utcnow() + timedelta(seconds=value)
        else:
            # Default to days
            return datetime.utcnow() + timedelta(days=value)
    except (ValueError, IndexError):
        # Default to 7 days
        return datetime.utcnow() + timedelta(days=7)


####################
# API Key Utilities
####################

def generate_api_key() -> str:
    """Generate a new API key"""
    return SECURITY.API_KEY_PREFIX + secrets.token_urlsafe(SECURITY.API_KEY_LENGTH)


def validate_api_key(api_key: str, db: Session) -> Optional[UserModel]:
    """Validate API key and return user"""
    try:
        user = db.query(User).filter(User.api_key == api_key).first()
        if user and user.is_active:
            return UserModel.model_validate(user)
        return None
    except Exception as e:
        log.error(f"API key validation error: {e}")
        return None


####################
# User Authentication
####################

def get_user_by_email(email: str, db: Session) -> Optional[User]:
    """Get user by email"""
    return db.query(User).filter(User.email == email).first()


def get_user_by_id(user_id: str, db: Session) -> Optional[User]:
    """Get user by ID"""
    return db.query(User).filter(User.id == user_id).first()


def authenticate_user(email: str, password: str, db: Session) -> Optional[UserModel]:
    """Authenticate user with email and password"""
    user = get_user_by_email(email, db)
    if not user:
        return None
    
    if not verify_password(password, user.password_hash):
        return None
    
    if not user.is_active:
        return None
    
    return UserModel.model_validate(user)


####################
# FastAPI Dependencies
####################

async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[UserModel]:
    """Get current authenticated user"""
    if not credentials:
        return None
    
    token = credentials.credentials
    
    # Try JWT token first
    try:
        payload = decode_token(token)
        user_id = payload.get("user_id")
        if user_id:
            user = get_user_by_id(user_id, db)
            if user and user.is_active:
                return UserModel.model_validate(user)
    except HTTPException:
        pass
    
    # Try API key
    user = validate_api_key(token, db)
    if user:
        return user
    
    return None


async def get_verified_user(
    current_user: Optional[UserModel] = Depends(get_current_user)
) -> UserModel:
    """Get verified user (must be authenticated)"""
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=ERROR_MESSAGES.UNAUTHORIZED
        )
    return current_user


async def get_admin_user(
    current_user: UserModel = Depends(get_verified_user)
) -> UserModel:
    """Get admin user (must be authenticated and admin)"""
    if current_user.role != ROLES.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=ERROR_MESSAGES.ACCESS_PROHIBITED
        )
    return current_user


####################
# Authorization Utilities
####################

def has_permission(user: UserModel, permission: str, resource_id: Optional[str] = None) -> bool:
    """Check if user has permission for a resource"""
    # Admin has all permissions
    if user.role == ROLES.ADMIN:
        return True
    
    # Basic permissions for regular users
    if permission in ["read", "create"]:
        return user.role == ROLES.USER
    
    # Write/delete permissions require ownership or admin
    if permission in ["write", "delete"] and resource_id:
        return user.id == resource_id or user.role == ROLES.ADMIN
    
    return False


def check_access_control(user: UserModel, access_control: Optional[dict]) -> bool:
    """Check if user has access based on access control settings"""
    if not access_control:
        return True
    
    # Admin always has access
    if user.role == ROLES.ADMIN:
        return True
    
    # Check public access
    if access_control.get("public", False):
        return True
    
    # Check user-specific access
    read_users = access_control.get("read", [])
    if user.id in read_users:
        return True
    
    # Check group access (if implemented)
    # groups = access_control.get("groups", [])
    # if any(group in user.groups for group in groups):
    #     return True
    
    return False
