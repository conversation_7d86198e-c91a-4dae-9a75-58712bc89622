"""
Chat management router for SmallDoge WebUI
Open source feature sharing - no authentication required
"""

import logging
from fastapi import APIRouter, HTTPException, status

from smalldoge_webui.constants import ERROR_MESSAGES

log = logging.getLogger(__name__)
router = APIRouter()


@router.get("/")
async def get_chats():
    """Get chats (open access)"""
    # TODO: Implement chat listing
    return {"chats": [], "total": 0}


@router.post("/")
async def create_chat():
    """Create a new chat (open access)"""
    # TODO: Implement chat creation
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Chat creation not yet implemented"
    )


@router.get("/{chat_id}")
async def get_chat(chat_id: str):
    """Get specific chat (open access)"""
    # TODO: Implement chat retrieval
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Chat retrieval not yet implemented"
    )


@router.put("/{chat_id}")
async def update_chat(chat_id: str):
    """Update chat (open access)"""
    # TODO: Implement chat update
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Chat update not yet implemented"
    )


@router.delete("/{chat_id}")
async def delete_chat(chat_id: str):
    """Delete chat (open access)"""
    # TODO: Implement chat deletion
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Chat deletion not yet implemented"
    )
