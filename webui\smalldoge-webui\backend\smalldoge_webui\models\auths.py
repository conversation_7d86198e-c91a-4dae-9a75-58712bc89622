"""
Authentication models for SmallDoge WebUI
Based on open-webui's authentication model patterns
"""

from typing import Optional
from pydantic import BaseModel, EmailStr


####################
# Token Models
####################

class Token(BaseModel):
    """JWT token response"""
    token: str
    token_type: str = "bearer"


class TokenData(BaseModel):
    """Token payload data"""
    user_id: Optional[str] = None
    email: Optional[str] = None
    role: Optional[str] = None


####################
# Authentication Forms
####################

class SigninForm(BaseModel):
    """User signin form"""
    email: EmailStr
    password: str


class SignupForm(BaseModel):
    """User signup form"""
    name: str
    email: EmailStr
    password: str
    profile_image_url: Optional[str] = "/user.png"


class PasswordResetForm(BaseModel):
    """Password reset form"""
    email: EmailStr


class PasswordResetConfirmForm(BaseModel):
    """Password reset confirmation form"""
    token: str
    new_password: str


####################
# Response Models
####################

class UserResponse(BaseModel):
    """User information in auth responses"""
    id: str
    email: str
    name: str
    role: str
    profile_image_url: str


class SigninResponse(Token, UserResponse):
    """Complete signin response with token and user info"""
    pass


class SignupResponse(BaseModel):
    """Signup response"""
    message: str
    user: UserResponse


####################
# API Key Models
####################

class ApiKey(BaseModel):
    """API key model"""
    api_key: Optional[str] = None


class ApiKeyForm(BaseModel):
    """API key creation form"""
    name: Optional[str] = None
    expires_at: Optional[int] = None


class ApiKeyResponse(BaseModel):
    """API key response"""
    api_key: str
    name: Optional[str] = None
    created_at: int
    expires_at: Optional[int] = None


####################
# OAuth Models
####################

class OAuthSigninForm(BaseModel):
    """OAuth signin form"""
    provider: str
    code: str
    state: Optional[str] = None


class OAuthUserInfo(BaseModel):
    """OAuth user information"""
    id: str
    email: str
    name: str
    picture: Optional[str] = None
    provider: str


####################
# Session Models
####################

class SessionInfo(BaseModel):
    """Session information"""
    user_id: str
    email: str
    role: str
    created_at: int
    expires_at: int
    last_active_at: int


class SessionResponse(BaseModel):
    """Session response"""
    session_id: str
    user: UserResponse
    created_at: int
    expires_at: int


####################
# Validation Models
####################

class EmailValidationForm(BaseModel):
    """Email validation form"""
    email: EmailStr


class PasswordValidationForm(BaseModel):
    """Password validation form"""
    password: str


class TokenValidationResponse(BaseModel):
    """Token validation response"""
    valid: bool
    user: Optional[UserResponse] = None
    expires_at: Optional[int] = None


####################
# Error Models
####################

class AuthError(BaseModel):
    """Authentication error response"""
    error: str
    message: str
    details: Optional[str] = None


class ValidationError(BaseModel):
    """Validation error response"""
    field: str
    message: str
    code: str
