"""
User management router for SmallDoge WebUI
"""

import logging
import time
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from smalldoge_webui.internal.db import get_db
from smalldoge_webui.models.users import (
    User, 
    UserModel, 
    UserResponse, 
    UserListResponse,
    UserUpdateForm,
    UserRoleUpdateForm,
    UpdatePasswordForm
)
from smalldoge_webui.utils.auth import (
    get_verified_user,
    get_admin_user,
    get_password_hash,
    verify_password,
    validate_password
)
from smalldoge_webui.constants import ERROR_MESSAGES, SUCCESS_MESSAGES, ROLES

log = logging.getLogger(__name__)
router = APIRouter()


####################
# User Information
####################

@router.get("/me", response_model=UserModel)
async def get_current_user_info(user: UserModel = Depends(get_verified_user)):
    """
    Get current user information
    
    Args:
        user: Authenticated user
    
    Returns:
        UserModel: Current user information
    """
    return user


@router.get("/", response_model=UserListResponse)
async def get_users(
    skip: int = 0,
    limit: int = 100,
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """
    Get list of users (admin only)
    
    Args:
        skip: Number of users to skip
        limit: Maximum number of users to return
        admin_user: Authenticated admin user
        db: Database session
    
    Returns:
        UserListResponse: List of users
    """
    try:
        users = db.query(User).offset(skip).limit(limit).all()
        total = db.query(User).count()
        
        user_models = [UserModel.model_validate(user) for user in users]
        
        return UserListResponse(users=user_models, total=total)
    
    except Exception as e:
        log.error(f"Error getting users: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: str,
    current_user: UserModel = Depends(get_verified_user),
    db: Session = Depends(get_db)
):
    """
    Get user by ID
    
    Args:
        user_id: User identifier
        current_user: Authenticated user
        db: Database session
    
    Returns:
        UserResponse: User information
    """
    try:
        # Users can only view their own profile unless they're admin
        if current_user.id != user_id and current_user.role != ROLES.ADMIN:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=ERROR_MESSAGES.ACCESS_PROHIBITED
            )
        
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.USER_NOT_FOUND
            )
        
        return UserResponse(
            id=user.id,
            name=user.name,
            email=user.email,
            role=user.role,
            profile_image_url=user.profile_image_url
        )
    
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error getting user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )


####################
# User Updates
####################

@router.put("/me", response_model=UserResponse)
async def update_current_user(
    form_data: UserUpdateForm,
    user: UserModel = Depends(get_verified_user),
    db: Session = Depends(get_db)
):
    """
    Update current user information
    
    Args:
        form_data: User update form
        user: Authenticated user
        db: Database session
    
    Returns:
        UserResponse: Updated user information
    """
    try:
        db_user = db.query(User).filter(User.id == user.id).first()
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.USER_NOT_FOUND
            )
        
        # Update fields if provided
        if form_data.name is not None:
            db_user.name = form_data.name
        
        if form_data.email is not None:
            # Check if email is already taken by another user
            existing_user = db.query(User).filter(
                User.email == form_data.email,
                User.id != user.id
            ).first()
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=ERROR_MESSAGES.EMAIL_TAKEN
                )
            db_user.email = form_data.email
        
        if form_data.profile_image_url is not None:
            db_user.profile_image_url = form_data.profile_image_url
        
        if form_data.settings is not None:
            db_user.settings = form_data.settings.model_dump()
        
        if form_data.info is not None:
            db_user.info = form_data.info
        
        db_user.updated_at = int(time.time())
        db.commit()
        db.refresh(db_user)
        
        log.info(f"User {user.email} updated their profile")
        
        return UserResponse(
            id=db_user.id,
            name=db_user.name,
            email=db_user.email,
            role=db_user.role,
            profile_image_url=db_user.profile_image_url
        )
    
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error updating user {user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )


@router.put("/password")
async def update_password(
    form_data: UpdatePasswordForm,
    user: UserModel = Depends(get_verified_user),
    db: Session = Depends(get_db)
):
    """
    Update user password
    
    Args:
        form_data: Password update form
        user: Authenticated user
        db: Database session
    
    Returns:
        dict: Success message
    """
    try:
        db_user = db.query(User).filter(User.id == user.id).first()
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.USER_NOT_FOUND
            )
        
        # Verify current password
        if not verify_password(form_data.current_password, db_user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=ERROR_MESSAGES.INVALID_PASSWORD
            )
        
        # Validate new password
        if not validate_password(form_data.new_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="New password does not meet requirements"
            )
        
        # Update password
        db_user.password_hash = get_password_hash(form_data.new_password)
        db_user.updated_at = int(time.time())
        db.commit()
        
        log.info(f"User {user.email} updated their password")
        
        return {"message": SUCCESS_MESSAGES.USER_UPDATED}
    
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error updating password for user {user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )


####################
# Admin Operations
####################

@router.put("/{user_id}/role", response_model=UserResponse)
async def update_user_role(
    user_id: str,
    form_data: UserRoleUpdateForm,
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """
    Update user role (admin only)
    
    Args:
        user_id: User identifier
        form_data: Role update form
        admin_user: Authenticated admin user
        db: Database session
    
    Returns:
        UserResponse: Updated user information
    """
    try:
        db_user = db.query(User).filter(User.id == user_id).first()
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.USER_NOT_FOUND
            )
        
        # Validate role
        if form_data.role not in [ROLES.ADMIN, ROLES.USER, ROLES.PENDING]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid role"
            )
        
        # Prevent admin from demoting themselves
        if db_user.id == admin_user.id and form_data.role != ROLES.ADMIN:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot change your own admin role"
            )
        
        db_user.role = form_data.role
        db_user.updated_at = int(time.time())
        db.commit()
        db.refresh(db_user)
        
        log.info(f"Admin {admin_user.email} updated role for user {db_user.email} to {form_data.role}")
        
        return UserResponse(
            id=db_user.id,
            name=db_user.name,
            email=db_user.email,
            role=db_user.role,
            profile_image_url=db_user.profile_image_url
        )
    
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error updating role for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )


@router.delete("/{user_id}")
async def delete_user(
    user_id: str,
    admin_user: UserModel = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """
    Delete user (admin only)
    
    Args:
        user_id: User identifier
        admin_user: Authenticated admin user
        db: Database session
    
    Returns:
        dict: Success message
    """
    try:
        db_user = db.query(User).filter(User.id == user_id).first()
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.USER_NOT_FOUND
            )
        
        # Prevent admin from deleting themselves
        if db_user.id == admin_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete your own account"
            )
        
        db.delete(db_user)
        db.commit()
        
        log.info(f"Admin {admin_user.email} deleted user {db_user.email}")
        
        return {"message": SUCCESS_MESSAGES.USER_DELETED}
    
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error deleting user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )
