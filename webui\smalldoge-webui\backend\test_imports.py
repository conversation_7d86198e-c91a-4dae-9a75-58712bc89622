#!/usr/bin/env python3
"""
Test script to check if all imports work correctly
"""

import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_imports():
    """Test all imports step by step"""
    
    print("Testing imports...")
    
    try:
        print("1. Testing env module...")
        from smalldoge_webui import env
        print("✓ env module imported successfully")
    except Exception as e:
        print(f"✗ env module failed: {e}")
        return False
    
    try:
        print("2. Testing constants module...")
        from smalldoge_webui import constants
        print("✓ constants module imported successfully")
    except Exception as e:
        print(f"✗ constants module failed: {e}")
        return False
    
    try:
        print("3. Testing internal.db module...")
        from smalldoge_webui.internal import db
        print("✓ internal.db module imported successfully")
    except Exception as e:
        print(f"✗ internal.db module failed: {e}")
        return False
    
    try:
        print("4. Testing models modules...")
        from smalldoge_webui.models import users, chats, models, auths
        print("✓ models modules imported successfully")
    except Exception as e:
        print(f"✗ models modules failed: {e}")
        return False
    
    try:
        print("5. Testing utils modules...")
        from smalldoge_webui.utils import auth, models as utils_models, chat
        print("✓ utils modules imported successfully")
    except Exception as e:
        print(f"✗ utils modules failed: {e}")
        return False
    
    try:
        print("6. Testing routers modules...")
        from smalldoge_webui.routers import auths, users, chats, models as router_models, openai
        print("✓ routers modules imported successfully")
    except Exception as e:
        print(f"✗ routers modules failed: {e}")
        return False
    
    try:
        print("7. Testing main module...")
        from smalldoge_webui import main
        print("✓ main module imported successfully")
    except Exception as e:
        print(f"✗ main module failed: {e}")
        return False
    
    print("\n✅ All imports successful!")
    return True

if __name__ == "__main__":
    success = test_imports()
    if not success:
        sys.exit(1)
    print("🎉 Import test completed successfully!")
