"""
Model management router for SmallDoge WebUI
"""

import logging
from typing import List

from fastapi import APIRouter, Depends, HTTPException, status

from smalldoge_webui.models.users import UserModel
from smalldoge_webui.models.models import ModelResponse, ModelListResponse
from smalldoge_webui.utils.auth import get_verified_user, get_admin_user
from smalldoge_webui.utils.models import (
    get_available_models,
    get_loaded_models,
    get_model_status,
    load_model,
    unload_model,
    health_check_model
)
from smalldoge_webui.constants import ERROR_MESSAGES

log = logging.getLogger(__name__)
router = APIRouter()


####################
# Model Information
####################

@router.get("/", response_model=List[str])
async def get_models(user: UserModel = Depends(get_verified_user)):
    """
    Get available models
    
    Args:
        user: Authenticated user
    
    Returns:
        List[str]: List of available model IDs
    """
    try:
        return get_available_models()
    except Exception as e:
        log.error(f"Error getting models: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )


@router.get("/loaded")
async def get_loaded_models_endpoint(user: UserModel = Depends(get_verified_user)):
    """
    Get currently loaded models
    
    Args:
        user: Authenticated user
    
    Returns:
        List[str]: List of loaded model IDs
    """
    try:
        return {"loaded_models": get_loaded_models()}
    except Exception as e:
        log.error(f"Error getting loaded models: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )


@router.get("/{model_id}/status")
async def get_model_status_endpoint(
    model_id: str,
    user: UserModel = Depends(get_verified_user)
):
    """
    Get model status
    
    Args:
        model_id: Model identifier
        user: Authenticated user
    
    Returns:
        dict: Model status information
    """
    try:
        return get_model_status(model_id)
    except Exception as e:
        log.error(f"Error getting model status for {model_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )


####################
# Model Management
####################

@router.post("/{model_id}/load")
async def load_model_endpoint(
    model_id: str,
    admin_user: UserModel = Depends(get_admin_user)
):
    """
    Load a model (admin only)
    
    Args:
        model_id: Model identifier
        admin_user: Authenticated admin user
    
    Returns:
        dict: Success message
    """
    try:
        available_models = get_available_models()
        if model_id not in available_models:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.MODEL_NOT_FOUND(model_id)
            )
        
        success = await load_model(model_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=ERROR_MESSAGES.MODEL_LOAD_ERROR(model_id)
            )
        
        log.info(f"Admin {admin_user.email} loaded model {model_id}")
        return {"message": f"Model {model_id} loaded successfully"}
    
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error loading model {model_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.MODEL_LOAD_ERROR(model_id, str(e))
        )


@router.post("/{model_id}/unload")
async def unload_model_endpoint(
    model_id: str,
    admin_user: UserModel = Depends(get_admin_user)
):
    """
    Unload a model (admin only)
    
    Args:
        model_id: Model identifier
        admin_user: Authenticated admin user
    
    Returns:
        dict: Success message
    """
    try:
        success = await unload_model(model_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to unload model {model_id}"
            )
        
        log.info(f"Admin {admin_user.email} unloaded model {model_id}")
        return {"message": f"Model {model_id} unloaded successfully"}
    
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error unloading model {model_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )


####################
# Model Health
####################

@router.get("/{model_id}/health")
async def health_check_model_endpoint(
    model_id: str,
    user: UserModel = Depends(get_verified_user)
):
    """
    Perform health check on a model
    
    Args:
        model_id: Model identifier
        user: Authenticated user
    
    Returns:
        dict: Health check results
    """
    try:
        return await health_check_model(model_id)
    except Exception as e:
        log.error(f"Error performing health check for model {model_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )
