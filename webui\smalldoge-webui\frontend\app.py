"""
Gradio Frontend for SmallDoge WebUI
Provides a chat interface that connects to the FastAPI backend
"""

import gradio as gr
import requests
import json
import time
from typing import List, Dict, Any, Optional
import os
from pathlib import Path

# Configuration
BACKEND_URL = os.getenv("BACKEND_URL", "http://localhost:8000")
API_BASE = f"{BACKEND_URL}/openai"


class SmallDogeWebUI:
    """Main Gradio interface for SmallDoge WebUI"""

    def __init__(self):
        self.available_models = []
        self.chat_history = []
        
    # Authentication removed for open source sharing
    
    def get_headers(self) -> Dict[str, str]:
        """Get headers for API requests"""
        return {"Content-Type": "application/json"}
    
    def load_models(self) -> List[str]:
        """Load available models from backend"""
        try:
            response = requests.get(
                f"{API_BASE}/models",
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                self.available_models = [model["id"] for model in data["data"]]
                return self.available_models
            else:
                return ["SmallDoge/Doge-160M"]  # Fallback
                
        except Exception as e:
            print(f"Error loading models: {e}")
            return ["SmallDoge/Doge-160M"]  # Fallback
    
    def chat_completion(
        self, 
        message: str, 
        history: List[List[str]], 
        model: str,
        temperature: float,
        max_tokens: int,
        top_p: float
    ) -> tuple[str, List[List[str]]]:
        """Generate chat completion"""
        if not message.strip():
            return "", history
        
        # Prepare messages for API
        messages = []
        
        # Add chat history
        for user_msg, assistant_msg in history:
            if user_msg:
                messages.append({"role": "user", "content": user_msg})
            if assistant_msg:
                messages.append({"role": "assistant", "content": assistant_msg})
        
        # Add current message
        messages.append({"role": "user", "content": message})
        
        try:
            # Make API request
            response = requests.post(
                f"{API_BASE}/chat/completions",
                headers=self.get_headers(),
                json={
                    "model": model,
                    "messages": messages,
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                    "top_p": top_p,
                    "stream": False
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                assistant_message = data["choices"][0]["message"]["content"]
                
                # Update history
                new_history = history + [[message, assistant_message]]
                return "", new_history
            else:
                error_msg = f"API Error: {response.status_code}"
                new_history = history + [[message, error_msg]]
                return "", new_history
                
        except Exception as e:
            error_msg = f"Error: {str(e)}"
            new_history = history + [[message, error_msg]]
            return "", new_history
    
    def clear_chat(self) -> List[List[str]]:
        """Clear chat history"""
        return []
    
    def create_interface(self) -> gr.Blocks:
        """Create the Gradio interface"""
        with gr.Blocks(
            title="SmallDoge WebUI",
            theme=gr.themes.Soft(),
            css="""
            .gradio-container {
                max-width: 1200px !important;
            }
            """
        ) as interface:
            
            gr.Markdown(
                """
                # 🐕 SmallDoge WebUI

                A model inference WebUI with Gradio frontend and open-webui-compatible backend.
                **Open source feature sharing - No login required!**
                """
            )
            
            # Main chat interface
            with gr.Row():
                with gr.Column(scale=3):
                    chatbot = gr.Chatbot(
                        label="Chat",
                        height=500,
                        show_label=True,
                        container=True
                    )
                    
                    with gr.Row():
                        msg_input = gr.Textbox(
                            label="Message",
                            placeholder="Type your message here...",
                            scale=4,
                            lines=2
                        )
                        send_btn = gr.Button("Send", variant="primary", scale=1)
                    
                    with gr.Row():
                        clear_btn = gr.Button("Clear Chat", variant="secondary")
                
                with gr.Column(scale=1):
                    gr.Markdown("### Settings")
                    
                    model_dropdown = gr.Dropdown(
                        label="Model",
                        choices=["SmallDoge/Doge-160M"],
                        value="SmallDoge/Doge-160M",
                        interactive=True
                    )
                    
                    temperature_slider = gr.Slider(
                        label="Temperature",
                        minimum=0.0,
                        maximum=2.0,
                        value=0.7,
                        step=0.1
                    )
                    
                    max_tokens_slider = gr.Slider(
                        label="Max Tokens",
                        minimum=1,
                        maximum=4096,
                        value=2048,
                        step=1
                    )
                    
                    top_p_slider = gr.Slider(
                        label="Top P",
                        minimum=0.0,
                        maximum=1.0,
                        value=0.9,
                        step=0.05
                    )
                    
                    refresh_models_btn = gr.Button("Refresh Models")
            
            # Event handlers
            def handle_refresh_models():
                models = self.load_models()
                return gr.update(choices=models, value=models[0] if models else None)

            # Connect events
            refresh_models_btn.click(
                handle_refresh_models,
                outputs=[model_dropdown]
            )
            
            send_btn.click(
                self.chat_completion,
                inputs=[
                    msg_input, 
                    chatbot, 
                    model_dropdown,
                    temperature_slider,
                    max_tokens_slider,
                    top_p_slider
                ],
                outputs=[msg_input, chatbot]
            )
            
            msg_input.submit(
                self.chat_completion,
                inputs=[
                    msg_input, 
                    chatbot, 
                    model_dropdown,
                    temperature_slider,
                    max_tokens_slider,
                    top_p_slider
                ],
                outputs=[msg_input, chatbot]
            )
            
            clear_btn.click(
                self.clear_chat,
                outputs=[chatbot]
            )
            
            # Load models on startup
            interface.load(
                handle_refresh_models,
                outputs=[model_dropdown]
            )
        
        return interface


def main():
    """Main entry point"""
    # Create the WebUI instance
    webui = SmallDogeWebUI()
    
    # Create and launch the interface
    interface = webui.create_interface()
    
    # Launch the interface
    interface.launch(
        server_name="127.0.0.1",
        server_port=7860,
        share=False,
        debug=True,
        show_error=True
    )


if __name__ == "__main__":
    main()
