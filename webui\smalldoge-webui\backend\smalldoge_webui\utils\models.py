"""
Model management utilities for SmallDoge WebUI
"""

import logging
from typing import List, Dict, Any, Optional

from smalldoge_webui.utils.transformers_inference import (
    model_manager,
    load_model as _load_model,
    unload_model as _unload_model,
    is_model_loaded as _is_model_loaded,
    get_loaded_models as _get_loaded_models,
    get_available_models as _get_available_models,
    get_model_info as _get_model_info,
)

log = logging.getLogger(__name__)


####################
# Model Management Functions
####################

async def load_model(model_id: str, **kwargs) -> bool:
    """
    Load a model for inference
    
    Args:
        model_id: Model identifier (e.g., "SmallDoge/Doge-160M")
        **kwargs: Additional loading parameters
    
    Returns:
        bool: True if model loaded successfully, False otherwise
    """
    try:
        log.info(f"Loading model: {model_id}")
        result = await _load_model(model_id, **kwargs)
        if result:
            log.info(f"Model {model_id} loaded successfully")
        else:
            log.error(f"Failed to load model {model_id}")
        return result
    except Exception as e:
        log.error(f"Error loading model {model_id}: {e}")
        return False


async def unload_model(model_id: str) -> bool:
    """
    Unload a model from memory
    
    Args:
        model_id: Model identifier
    
    Returns:
        bool: True if model unloaded successfully, False otherwise
    """
    try:
        log.info(f"Unloading model: {model_id}")
        result = await _unload_model(model_id)
        if result:
            log.info(f"Model {model_id} unloaded successfully")
        else:
            log.error(f"Failed to unload model {model_id}")
        return result
    except Exception as e:
        log.error(f"Error unloading model {model_id}: {e}")
        return False


def is_model_loaded(model_id: str) -> bool:
    """
    Check if a model is currently loaded
    
    Args:
        model_id: Model identifier
    
    Returns:
        bool: True if model is loaded, False otherwise
    """
    return _is_model_loaded(model_id)


def get_loaded_models() -> List[str]:
    """
    Get list of currently loaded models
    
    Returns:
        List[str]: List of loaded model identifiers
    """
    return _get_loaded_models()


def get_available_models() -> List[str]:
    """
    Get list of available models
    
    Returns:
        List[str]: List of available model identifiers
    """
    return _get_available_models()


def get_model_info(model_id: str) -> Optional[Dict[str, Any]]:
    """
    Get information about a model
    
    Args:
        model_id: Model identifier
    
    Returns:
        Optional[Dict[str, Any]]: Model information or None if not found
    """
    return _get_model_info(model_id)


def get_model_status(model_id: str) -> Dict[str, Any]:
    """
    Get detailed status of a model
    
    Args:
        model_id: Model identifier
    
    Returns:
        Dict[str, Any]: Model status information
    """
    status = {
        "id": model_id,
        "is_loaded": is_model_loaded(model_id),
        "is_available": model_id in get_available_models(),
    }
    
    if status["is_loaded"]:
        info = get_model_info(model_id)
        if info:
            status.update({
                "load_time": info.get("load_time"),
                "device": info.get("device"),
                "torch_dtype": info.get("torch_dtype"),
            })
    
    return status


async def load_default_model() -> bool:
    """
    Load the default model
    
    Returns:
        bool: True if default model loaded successfully, False otherwise
    """
    from smalldoge_webui.env import DEFAULT_MODELS
    
    default_model = DEFAULT_MODELS.split(",")[0].strip()
    log.info(f"Loading default model: {default_model}")
    
    return await load_model(default_model)


async def ensure_model_loaded(model_id: str) -> bool:
    """
    Ensure a model is loaded, loading it if necessary

    Args:
        model_id: Model identifier

    Returns:
        bool: True if model is loaded, False if loading failed
    """
    if is_model_loaded(model_id):
        return True

    return await load_model(model_id)


def validate_model_id(model_id: str) -> bool:
    """
    Validate if a model ID is valid and available
    
    Args:
        model_id: Model identifier to validate
    
    Returns:
        bool: True if model ID is valid, False otherwise
    """
    available_models = get_available_models()
    return model_id in available_models


def get_model_capabilities(model_id: str) -> List[str]:
    """
    Get capabilities of a model
    
    Args:
        model_id: Model identifier
    
    Returns:
        List[str]: List of model capabilities
    """
    # Default capabilities for all models
    capabilities = ["text-generation", "chat-completion"]
    
    # Add model-specific capabilities based on model ID
    if "doge" in model_id.lower():
        capabilities.extend(["conversation", "instruction-following"])
    
    return capabilities


def get_model_context_length(model_id: str) -> int:
    """
    Get context length for a model
    
    Args:
        model_id: Model identifier
    
    Returns:
        int: Context length in tokens
    """
    # Default context length
    default_context = 2048
    
    # Model-specific context lengths
    context_lengths = {
        "SmallDoge/Doge-160M": 2048,
        "SmallDoge/Doge-1B": 4096,
        "SmallDoge/Doge-7B": 8192,
    }
    
    return context_lengths.get(model_id, default_context)


####################
# Model Health Checks
####################

async def health_check_model(model_id: str) -> Dict[str, Any]:
    """
    Perform health check on a model
    
    Args:
        model_id: Model identifier
    
    Returns:
        Dict[str, Any]: Health check results
    """
    import time
    
    health_status = {
        "model_id": model_id,
        "status": "unknown",
        "message": "",
        "timestamp": int(time.time()),
        "response_time": None,
    }
    
    try:
        start_time = time.time()
        
        # Check if model is loaded
        if not is_model_loaded(model_id):
            health_status.update({
                "status": "not_loaded",
                "message": "Model is not loaded"
            })
            return health_status
        
        # Try a simple inference
        from smalldoge_webui.models.chats import ChatMessage
        from smalldoge_webui.utils.transformers_inference import generate_completion
        
        test_messages = [
            ChatMessage(role="user", content="Hello")
        ]
        
        # Generate a short response to test the model
        response_generated = False
        async for token in generate_completion(model_id, test_messages, stream=True):
            if token.strip():
                response_generated = True
                break
        
        response_time = time.time() - start_time
        
        if response_generated:
            health_status.update({
                "status": "healthy",
                "message": "Model is responding normally",
                "response_time": response_time
            })
        else:
            health_status.update({
                "status": "unhealthy",
                "message": "Model did not generate response",
                "response_time": response_time
            })
    
    except Exception as e:
        health_status.update({
            "status": "error",
            "message": f"Health check failed: {str(e)}"
        })
    
    return health_status


async def health_check_all_models() -> Dict[str, Dict[str, Any]]:
    """
    Perform health check on all loaded models
    
    Returns:
        Dict[str, Dict[str, Any]]: Health check results for all models
    """
    results = {}
    loaded_models = get_loaded_models()
    
    for model_id in loaded_models:
        results[model_id] = await health_check_model(model_id)
    
    return results
